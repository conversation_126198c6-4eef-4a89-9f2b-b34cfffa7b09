import { AppState } from '../types';
import { 
  sampleUsers, 
  sampleClasses, 
  sampleSubjects, 
  sampleAttendance, 
  sampleGrades, 
  sampleAcademicYear 
} from './sampleData';

const STORAGE_KEY = 'hikmaah_app_data';
const STORAGE_VERSION = '1.0.0';

// Default app state with sample data
const defaultAppState: AppState = {
  users: sampleUsers,
  classes: sampleClasses,
  subjects: sampleSubjects,
  attendance: sampleAttendance,
  grades: sampleGrades,
  virtualClasses: [],
  activities: [],
  notifications: [],
  academicYear: sampleAcademicYear,
  theme: 'light'
};

// Storage service class
export class StorageService {
  private static instance: StorageService;
  
  private constructor() {}
  
  public static getInstance(): StorageService {
    if (!StorageService.instance) {
      StorageService.instance = new StorageService();
    }
    return StorageService.instance;
  }
  
  // Initialize storage with default data if not exists
  public initializeStorage(): void {
    try {
      const existingData = localStorage.getItem(STORAGE_KEY);
      
      if (!existingData) {
        console.log('Initializing storage with sample data...');
        this.saveAppState(defaultAppState);
      } else {
        // Check if we need to migrate data
        const parsed = JSON.parse(existingData);
        if (!parsed.version || parsed.version !== STORAGE_VERSION) {
          console.log('Migrating storage data...');
          this.migrateData(parsed);
        }
      }
    } catch (error) {
      console.error('Error initializing storage:', error);
      // Fallback to default data
      this.saveAppState(defaultAppState);
    }
  }
  
  // Get complete app state
  public getAppState(): AppState {
    try {
      const data = localStorage.getItem(STORAGE_KEY);
      if (!data) {
        return defaultAppState;
      }
      
      const parsed = JSON.parse(data);
      return parsed.data || defaultAppState;
    } catch (error) {
      console.error('Error getting app state:', error);
      return defaultAppState;
    }
  }
  
  // Save complete app state
  public saveAppState(state: AppState): void {
    try {
      const dataToSave = {
        version: STORAGE_VERSION,
        timestamp: new Date().toISOString(),
        data: state
      };
      
      localStorage.setItem(STORAGE_KEY, JSON.stringify(dataToSave));
    } catch (error) {
      console.error('Error saving app state:', error);
      throw new Error('Failed to save data to local storage');
    }
  }
  
  // Update specific part of app state
  public updateAppState(updates: Partial<AppState>): void {
    const currentState = this.getAppState();
    const newState = { ...currentState, ...updates };
    this.saveAppState(newState);
  }
  
  // Clear all data and reset to defaults
  public resetToDefaults(): void {
    try {
      localStorage.removeItem(STORAGE_KEY);
      this.initializeStorage();
    } catch (error) {
      console.error('Error resetting storage:', error);
    }
  }
  
  // Export data as JSON
  public exportData(): string {
    const state = this.getAppState();
    return JSON.stringify(state, null, 2);
  }
  
  // Import data from JSON
  public importData(jsonData: string): boolean {
    try {
      const data = JSON.parse(jsonData);
      
      // Validate the data structure
      if (this.validateAppState(data)) {
        this.saveAppState(data);
        return true;
      } else {
        throw new Error('Invalid data structure');
      }
    } catch (error) {
      console.error('Error importing data:', error);
      return false;
    }
  }
  
  // Get storage usage info
  public getStorageInfo(): { used: number; available: number; percentage: number } {
    try {
      const data = localStorage.getItem(STORAGE_KEY);
      const used = data ? new Blob([data]).size : 0;
      const available = 5 * 1024 * 1024; // Assume 5MB limit for localStorage
      const percentage = (used / available) * 100;
      
      return { used, available, percentage };
    } catch (error) {
      console.error('Error getting storage info:', error);
      return { used: 0, available: 0, percentage: 0 };
    }
  }
  
  // Backup data to file
  public downloadBackup(): void {
    try {
      const data = this.exportData();
      const blob = new Blob([data], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `hikmaah_backup_${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading backup:', error);
    }
  }
  
  // Validate app state structure
  private validateAppState(data: any): data is AppState {
    return (
      data &&
      typeof data === 'object' &&
      Array.isArray(data.users) &&
      Array.isArray(data.classes) &&
      Array.isArray(data.subjects) &&
      Array.isArray(data.attendance) &&
      Array.isArray(data.grades) &&
      Array.isArray(data.virtualClasses) &&
      Array.isArray(data.activities) &&
      Array.isArray(data.notifications) &&
      (data.academicYear === null || typeof data.academicYear === 'object') &&
      (data.theme === 'light' || data.theme === 'dark')
    );
  }
  
  // Migrate data from older versions
  private migrateData(oldData: any): void {
    try {
      // For now, just reset to defaults if version mismatch
      // In future versions, implement proper migration logic
      console.log('Data migration needed, resetting to defaults...');
      this.saveAppState(defaultAppState);
    } catch (error) {
      console.error('Error migrating data:', error);
      this.saveAppState(defaultAppState);
    }
  }
}

// Utility functions for specific data operations
export const storageUtils = {
  // User operations
  addUser: (user: any) => {
    const storage = StorageService.getInstance();
    const state = storage.getAppState();
    state.users.push(user);
    storage.saveAppState(state);
  },
  
  updateUser: (userId: string, updates: any) => {
    const storage = StorageService.getInstance();
    const state = storage.getAppState();
    const userIndex = state.users.findIndex(u => u.id === userId);
    if (userIndex !== -1) {
      state.users[userIndex] = { ...state.users[userIndex], ...updates };
      storage.saveAppState(state);
    }
  },
  
  deleteUser: (userId: string) => {
    const storage = StorageService.getInstance();
    const state = storage.getAppState();
    state.users = state.users.filter(u => u.id !== userId);
    storage.saveAppState(state);
  },
  
  // Attendance operations
  addAttendanceRecord: (record: any) => {
    const storage = StorageService.getInstance();
    const state = storage.getAppState();
    state.attendance.push(record);
    storage.saveAppState(state);
  },
  
  updateAttendanceRecord: (recordId: string, updates: any) => {
    const storage = StorageService.getInstance();
    const state = storage.getAppState();
    const recordIndex = state.attendance.findIndex(r => r.id === recordId);
    if (recordIndex !== -1) {
      state.attendance[recordIndex] = { ...state.attendance[recordIndex], ...updates };
      storage.saveAppState(state);
    }
  },
  
  // Grade operations
  addGradeRecord: (record: any) => {
    const storage = StorageService.getInstance();
    const state = storage.getAppState();
    state.grades.push(record);
    storage.saveAppState(state);
  },
  
  // Theme operations
  setTheme: (theme: 'light' | 'dark') => {
    const storage = StorageService.getInstance();
    const state = storage.getAppState();
    state.theme = theme;
    storage.saveAppState(state);
  }
};

// Export singleton instance
export const storage = StorageService.getInstance();
