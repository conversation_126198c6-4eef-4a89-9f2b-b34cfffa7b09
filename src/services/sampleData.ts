import { 
  User, 
  Admin, 
  Teacher, 
  Student, 
  Class, 
  Subject, 
  AttendanceRecord, 
  GradeRecord, 
  VirtualClass, 
  Activity, 
  AcademicYear 
} from '../types';

// Sample Admin
export const sampleAdmin: Admin = {
  id: 'admin-1',
  email: '<EMAIL>',
  password: 'admin123',
  role: 'admin',
  firstName: '<PERSON>',
  lastName: 'Administrator',
  avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  isActive: true,
  permissions: ['manage_users', 'manage_classes', 'view_reports', 'system_settings']
};

// Sample Teachers
export const sampleTeachers: Teacher[] = [
  {
    id: 'teacher-1',
    email: '<EMAIL>',
    password: 'teacher123',
    role: 'teacher',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    isActive: true,
    employeeId: 'EMP001',
    subjects: ['Mathematics', 'Physics'],
    classes: ['10A', '10B'],
    department: 'Science',
    phoneNumber: '******-0101',
    address: '123 Teacher Lane, Education City'
  },
  {
    id: 'teacher-2',
    email: '<EMAIL>',
    password: 'teacher123',
    role: 'teacher',
    firstName: 'Michael',
    lastName: 'Davis',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    isActive: true,
    employeeId: 'EMP002',
    subjects: ['English', 'History'],
    classes: ['10A', '10B'],
    department: 'Humanities',
    phoneNumber: '******-0102',
    address: '456 Scholar Street, Education City'
  }
];

// Sample Students
export const sampleStudents: Student[] = [
  {
    id: 'student-1',
    email: '<EMAIL>',
    password: 'student123',
    role: 'student',
    firstName: 'Emma',
    lastName: 'Wilson',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    isActive: true,
    studentId: 'STU001',
    class: '10A',
    grade: '10',
    dateOfBirth: '2008-05-15',
    parentName: 'Robert Wilson',
    parentPhone: '******-1001',
    parentEmail: '<EMAIL>',
    address: '789 Student Avenue, Education City',
    enrollmentDate: '2024-01-01',
    bloodGroup: 'A+',
    emergencyContact: '******-1002'
  },
  {
    id: 'student-2',
    email: '<EMAIL>',
    password: 'student123',
    role: 'student',
    firstName: 'James',
    lastName: 'Brown',
    avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    isActive: true,
    studentId: 'STU002',
    class: '10A',
    grade: '10',
    dateOfBirth: '2008-08-22',
    parentName: 'Lisa Brown',
    parentPhone: '******-1003',
    parentEmail: '<EMAIL>',
    address: '321 Learning Lane, Education City',
    enrollmentDate: '2024-01-01',
    bloodGroup: 'B+',
    emergencyContact: '******-1004'
  },
  {
    id: 'student-3',
    email: '<EMAIL>',
    password: 'student123',
    role: 'student',
    firstName: 'Sophia',
    lastName: 'Garcia',
    avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    isActive: true,
    studentId: 'STU003',
    class: '10B',
    grade: '10',
    dateOfBirth: '2008-03-10',
    parentName: 'Carlos Garcia',
    parentPhone: '******-1005',
    parentEmail: '<EMAIL>',
    address: '654 Knowledge Street, Education City',
    enrollmentDate: '2024-01-01',
    bloodGroup: 'O+',
    emergencyContact: '******-1006'
  },
  {
    id: 'student-4',
    email: '<EMAIL>',
    password: 'student123',
    role: 'student',
    firstName: 'Liam',
    lastName: 'Martinez',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    isActive: true,
    studentId: 'STU004',
    class: '10B',
    grade: '10',
    dateOfBirth: '2008-11-05',
    parentName: 'Maria Martinez',
    parentPhone: '******-1007',
    parentEmail: '<EMAIL>',
    address: '987 Education Boulevard, Education City',
    enrollmentDate: '2024-01-01',
    bloodGroup: 'AB+',
    emergencyContact: '******-1008'
  }
];

// Sample Classes
export const sampleClasses: Class[] = [
  {
    id: 'class-1',
    name: 'Grade 10 Section A',
    grade: '10',
    section: 'A',
    teacherId: 'teacher-1',
    subjects: ['Mathematics', 'Physics', 'English', 'History'],
    students: ['student-1', 'student-2'],
    academicYear: '2024-2025',
    capacity: 30,
    room: 'Room 101'
  },
  {
    id: 'class-2',
    name: 'Grade 10 Section B',
    grade: '10',
    section: 'B',
    teacherId: 'teacher-2',
    subjects: ['Mathematics', 'Physics', 'English', 'History'],
    students: ['student-3', 'student-4'],
    academicYear: '2024-2025',
    capacity: 30,
    room: 'Room 102'
  }
];

// Sample Subjects
export const sampleSubjects: Subject[] = [
  {
    id: 'subject-1',
    name: 'Mathematics',
    code: 'MATH10',
    description: 'Advanced Mathematics for Grade 10',
    credits: 4,
    department: 'Science'
  },
  {
    id: 'subject-2',
    name: 'Physics',
    code: 'PHYS10',
    description: 'Introduction to Physics',
    credits: 4,
    department: 'Science'
  },
  {
    id: 'subject-3',
    name: 'English',
    code: 'ENG10',
    description: 'English Language and Literature',
    credits: 3,
    department: 'Humanities'
  },
  {
    id: 'subject-4',
    name: 'History',
    code: 'HIST10',
    description: 'World History',
    credits: 3,
    department: 'Humanities'
  }
];

// Sample Academic Year
export const sampleAcademicYear: AcademicYear = {
  id: 'ay-2024-2025',
  year: '2024-2025',
  startDate: '2024-09-01',
  endDate: '2025-06-30',
  isActive: true,
  terms: [
    {
      id: 'term-1',
      name: 'Fall Term',
      startDate: '2024-09-01',
      endDate: '2024-12-20',
      isActive: true
    },
    {
      id: 'term-2',
      name: 'Spring Term',
      startDate: '2025-01-15',
      endDate: '2025-06-30',
      isActive: false
    }
  ]
};

// Combine all users
export const sampleUsers: User[] = [
  sampleAdmin,
  ...sampleTeachers,
  ...sampleStudents
];

// Generate sample attendance records
export const generateSampleAttendance = (): AttendanceRecord[] => {
  const records: AttendanceRecord[] = [];
  const statuses: AttendanceRecord['status'][] = ['present', 'absent', 'late', 'excused'];
  
  // Generate attendance for the last 30 days
  for (let i = 0; i < 30; i++) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    const dateStr = date.toISOString().split('T')[0];
    
    sampleStudents.forEach((student, index) => {
      // 85% chance of being present
      const randomStatus = Math.random() < 0.85 ? 'present' : statuses[Math.floor(Math.random() * statuses.length)];
      
      records.push({
        id: `attendance-${student.id}-${dateStr}`,
        studentId: student.id,
        classId: student.class === '10A' ? 'class-1' : 'class-2',
        date: dateStr,
        status: randomStatus,
        markedBy: student.class === '10A' ? 'teacher-1' : 'teacher-2',
        markedAt: `${dateStr}T09:00:00Z`,
        notes: randomStatus === 'absent' ? 'Sick leave' : undefined
      });
    });
  }
  
  return records;
};

// Generate sample grades
export const generateSampleGrades = (): GradeRecord[] => {
  const records: GradeRecord[] = [];
  const types: GradeRecord['type'][] = ['quiz', 'test', 'assignment', 'project'];
  
  sampleStudents.forEach(student => {
    sampleSubjects.forEach(subject => {
      // Generate 3-5 grades per subject per student
      const numGrades = Math.floor(Math.random() * 3) + 3;
      
      for (let i = 0; i < numGrades; i++) {
        const type = types[Math.floor(Math.random() * types.length)];
        const maxScore = type === 'quiz' ? 20 : type === 'test' ? 100 : type === 'assignment' ? 50 : 100;
        const score = Math.floor(Math.random() * (maxScore * 0.4)) + (maxScore * 0.6); // 60-100% range
        
        const date = new Date();
        date.setDate(date.getDate() - Math.floor(Math.random() * 60));
        
        records.push({
          id: `grade-${student.id}-${subject.id}-${i}`,
          studentId: student.id,
          subjectId: subject.id,
          classId: student.class === '10A' ? 'class-1' : 'class-2',
          type,
          score,
          maxScore,
          date: date.toISOString().split('T')[0],
          teacherId: student.class === '10A' ? 'teacher-1' : 'teacher-2',
          comments: score < maxScore * 0.7 ? 'Needs improvement' : score > maxScore * 0.9 ? 'Excellent work!' : undefined
        });
      }
    });
  });
  
  return records;
};

export const sampleAttendance = generateSampleAttendance();
export const sampleGrades = generateSampleGrades();
