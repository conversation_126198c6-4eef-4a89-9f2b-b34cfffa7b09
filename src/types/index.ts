// User roles
export type UserRole = 'admin' | 'teacher' | 'student';

// Base user interface
export interface User {
  id: string;
  email: string;
  password: string;
  role: UserRole;
  firstName: string;
  lastName: string;
  avatar?: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
}

// Admin specific data
export interface Admin extends User {
  role: 'admin';
  permissions: string[];
}

// Teacher specific data
export interface Teacher extends User {
  role: 'teacher';
  employeeId: string;
  subjects: string[];
  classes: string[];
  department: string;
  phoneNumber?: string;
  address?: string;
}

// Student specific data
export interface Student extends User {
  role: 'student';
  studentId: string;
  class: string;
  grade: string;
  dateOfBirth: string;
  parentName: string;
  parentPhone: string;
  parentEmail: string;
  address: string;
  enrollmentDate: string;
  bloodGroup?: string;
  emergencyContact?: string;
}

// Class/Grade structure
export interface Class {
  id: string;
  name: string;
  grade: string;
  section: string;
  teacherId: string;
  subjects: string[];
  students: string[];
  academicYear: string;
  capacity: number;
  room?: string;
}

// Subject structure
export interface Subject {
  id: string;
  name: string;
  code: string;
  description?: string;
  credits: number;
  department: string;
}

// Attendance record
export interface AttendanceRecord {
  id: string;
  studentId: string;
  classId: string;
  date: string;
  status: 'present' | 'absent' | 'late' | 'excused';
  markedBy: string;
  markedAt: string;
  notes?: string;
}

// Grade/Score record
export interface GradeRecord {
  id: string;
  studentId: string;
  subjectId: string;
  classId: string;
  type: 'quiz' | 'test' | 'assignment' | 'project' | 'final';
  score: number;
  maxScore: number;
  date: string;
  teacherId: string;
  comments?: string;
}

// Virtual class session
export interface VirtualClass {
  id: string;
  title: string;
  description?: string;
  classId: string;
  teacherId: string;
  scheduledDate: string;
  startTime: string;
  endTime: string;
  meetingLink: string;
  recordingLink?: string;
  status: 'scheduled' | 'ongoing' | 'completed' | 'cancelled';
  attendees: string[];
}

// Activity/Achievement record
export interface Activity {
  id: string;
  studentId: string;
  title: string;
  description: string;
  type: 'academic' | 'sports' | 'cultural' | 'community' | 'leadership';
  date: string;
  points?: number;
  certificate?: string;
  addedBy: string;
}

// Notification
export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  isRead: boolean;
  createdAt: string;
  actionUrl?: string;
}

// Academic year configuration
export interface AcademicYear {
  id: string;
  year: string;
  startDate: string;
  endDate: string;
  isActive: boolean;
  terms: Term[];
}

export interface Term {
  id: string;
  name: string;
  startDate: string;
  endDate: string;
  isActive: boolean;
}

// Dashboard statistics
export interface DashboardStats {
  totalStudents: number;
  totalTeachers: number;
  totalClasses: number;
  totalSubjects: number;
  attendanceRate: number;
  activeUsers: number;
}

// Authentication context
export interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => void;
  isLoading: boolean;
  error: string | null;
}

// App state for global state management
export interface AppState {
  users: User[];
  classes: Class[];
  subjects: Subject[];
  attendance: AttendanceRecord[];
  grades: GradeRecord[];
  virtualClasses: VirtualClass[];
  activities: Activity[];
  notifications: Notification[];
  academicYear: AcademicYear | null;
  theme: 'light' | 'dark';
}

// Action types for state management
export type AppAction =
  | { type: 'SET_USERS'; payload: User[] }
  | { type: 'ADD_USER'; payload: User }
  | { type: 'UPDATE_USER'; payload: { id: string; updates: Partial<User> } }
  | { type: 'DELETE_USER'; payload: string }
  | { type: 'SET_CLASSES'; payload: Class[] }
  | { type: 'ADD_CLASS'; payload: Class }
  | { type: 'UPDATE_CLASS'; payload: { id: string; updates: Partial<Class> } }
  | { type: 'DELETE_CLASS'; payload: string }
  | { type: 'SET_ATTENDANCE'; payload: AttendanceRecord[] }
  | { type: 'ADD_ATTENDANCE'; payload: AttendanceRecord }
  | { type: 'SET_GRADES'; payload: GradeRecord[] }
  | { type: 'ADD_GRADE'; payload: GradeRecord }
  | { type: 'SET_VIRTUAL_CLASSES'; payload: VirtualClass[] }
  | { type: 'ADD_VIRTUAL_CLASS'; payload: VirtualClass }
  | { type: 'SET_ACTIVITIES'; payload: Activity[] }
  | { type: 'ADD_ACTIVITY'; payload: Activity }
  | { type: 'SET_NOTIFICATIONS'; payload: Notification[] }
  | { type: 'ADD_NOTIFICATION'; payload: Notification }
  | { type: 'MARK_NOTIFICATION_READ'; payload: string }
  | { type: 'SET_THEME'; payload: 'light' | 'dark' }
  | { type: 'SET_ACADEMIC_YEAR'; payload: AcademicYear };

// Form data types
export interface LoginFormData {
  email: string;
  password: string;
  rememberMe: boolean;
}

export interface StudentFormData {
  firstName: string;
  lastName: string;
  email: string;
  studentId: string;
  class: string;
  grade: string;
  dateOfBirth: string;
  parentName: string;
  parentPhone: string;
  parentEmail: string;
  address: string;
  bloodGroup?: string;
  emergencyContact?: string;
}

export interface TeacherFormData {
  firstName: string;
  lastName: string;
  email: string;
  employeeId: string;
  subjects: string[];
  classes: string[];
  department: string;
  phoneNumber?: string;
  address?: string;
}

// API response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Filter and search types
export interface StudentFilter {
  class?: string;
  grade?: string;
  status?: 'active' | 'inactive';
  search?: string;
}

export interface AttendanceFilter {
  classId?: string;
  date?: string;
  status?: AttendanceRecord['status'];
}

export interface GradeFilter {
  classId?: string;
  subjectId?: string;
  type?: GradeRecord['type'];
  dateRange?: { start: string; end: string };
}
