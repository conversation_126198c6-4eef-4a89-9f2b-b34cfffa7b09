import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { User, AuthContextType } from '../types';
import { storage } from '../services/storage';

// Auth state interface
interface AuthState {
  user: User | null;
  isLoading: boolean;
  error: string | null;
  isInitialized: boolean;
}

// Auth actions
type AuthAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_USER'; payload: User | null }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_INITIALIZED'; payload: boolean }
  | { type: 'LOGOUT' };

// Initial state
const initialState: AuthState = {
  user: null,
  isLoading: true,
  error: null,
  isInitialized: false
};

// Auth reducer
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_USER':
      return { ...state, user: action.payload, error: null };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    case 'SET_INITIALIZED':
      return { ...state, isInitialized: action.payload };
    case 'LOGOUT':
      return { ...state, user: null, error: null };
    default:
      return state;
  }
};

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth provider props
interface AuthProviderProps {
  children: ReactNode;
}

// Session storage keys
const SESSION_KEY = 'hikmaah_session';
const REMEMBER_KEY = 'hikmaah_remember';

// Auth provider component
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Initialize auth state on mount
  useEffect(() => {
    initializeAuth();
  }, []);

  // Initialize authentication
  const initializeAuth = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      // Initialize storage first
      storage.initializeStorage();
      
      // Check for existing session
      const sessionData = sessionStorage.getItem(SESSION_KEY);
      const rememberData = localStorage.getItem(REMEMBER_KEY);
      
      let userData = null;
      
      if (sessionData) {
        userData = JSON.parse(sessionData);
      } else if (rememberData) {
        userData = JSON.parse(rememberData);
        // Restore session
        sessionStorage.setItem(SESSION_KEY, rememberData);
      }
      
      if (userData) {
        // Validate user still exists in storage
        const appState = storage.getAppState();
        const user = appState.users.find(u => u.id === userData.id);
        
        if (user && user.isActive) {
          dispatch({ type: 'SET_USER', payload: user });
        } else {
          // User no longer exists or is inactive, clear session
          clearSession();
        }
      }
    } catch (error) {
      console.error('Error initializing auth:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Failed to initialize authentication' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
      dispatch({ type: 'SET_INITIALIZED', payload: true });
    }
  };

  // Login function
  const login = async (email: string, password: string, rememberMe = false): Promise<boolean> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'SET_ERROR', payload: null });

      // Get users from storage
      const appState = storage.getAppState();
      const user = appState.users.find(
        u => u.email.toLowerCase() === email.toLowerCase() && u.password === password
      );

      if (!user) {
        dispatch({ type: 'SET_ERROR', payload: 'Invalid email or password' });
        return false;
      }

      if (!user.isActive) {
        dispatch({ type: 'SET_ERROR', payload: 'Account is deactivated. Please contact administrator.' });
        return false;
      }

      // Create session
      const sessionData = {
        id: user.id,
        email: user.email,
        role: user.role,
        loginTime: new Date().toISOString()
      };

      // Save session
      sessionStorage.setItem(SESSION_KEY, JSON.stringify(sessionData));
      
      if (rememberMe) {
        localStorage.setItem(REMEMBER_KEY, JSON.stringify(sessionData));
      }

      // Update user's last login time
      const updatedUser = {
        ...user,
        updatedAt: new Date().toISOString()
      };

      // Update user in storage
      const userIndex = appState.users.findIndex(u => u.id === user.id);
      if (userIndex !== -1) {
        appState.users[userIndex] = updatedUser;
        storage.saveAppState(appState);
      }

      dispatch({ type: 'SET_USER', payload: updatedUser });
      return true;
    } catch (error) {
      console.error('Login error:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Login failed. Please try again.' });
      return false;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  // Logout function
  const logout = () => {
    try {
      clearSession();
      dispatch({ type: 'LOGOUT' });
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // Clear session data
  const clearSession = () => {
    sessionStorage.removeItem(SESSION_KEY);
    localStorage.removeItem(REMEMBER_KEY);
  };

  // Check if user has permission
  const hasPermission = (permission: string): boolean => {
    if (!state.user) return false;
    
    if (state.user.role === 'admin') {
      return (state.user as any).permissions?.includes(permission) || true;
    }
    
    // Teachers have limited permissions
    if (state.user.role === 'teacher') {
      const teacherPermissions = ['view_students', 'manage_attendance', 'manage_grades', 'view_classes'];
      return teacherPermissions.includes(permission);
    }
    
    // Students have very limited permissions
    if (state.user.role === 'student') {
      const studentPermissions = ['view_profile', 'view_attendance', 'view_grades', 'view_classes'];
      return studentPermissions.includes(permission);
    }
    
    return false;
  };

  // Get user display name
  const getUserDisplayName = (): string => {
    if (!state.user) return '';
    return `${state.user.firstName} ${state.user.lastName}`;
  };

  // Check if session is valid
  const isSessionValid = (): boolean => {
    const sessionData = sessionStorage.getItem(SESSION_KEY);
    if (!sessionData) return false;
    
    try {
      const session = JSON.parse(sessionData);
      const loginTime = new Date(session.loginTime);
      const now = new Date();
      const hoursDiff = (now.getTime() - loginTime.getTime()) / (1000 * 60 * 60);
      
      // Session expires after 8 hours
      return hoursDiff < 8;
    } catch {
      return false;
    }
  };

  // Auto logout on session expiry
  useEffect(() => {
    if (state.user && !isSessionValid()) {
      logout();
    }
  }, [state.user]);

  // Context value
  const contextValue: AuthContextType = {
    user: state.user,
    login,
    logout,
    isLoading: state.isLoading,
    error: state.error,
    // Additional helper methods
    hasPermission,
    getUserDisplayName,
    isInitialized: state.isInitialized
  } as any;

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// HOC for protected routes
export const withAuth = <P extends object>(
  Component: React.ComponentType<P>,
  requiredRole?: string[]
) => {
  return (props: P) => {
    const { user, isLoading } = useAuth();
    
    if (isLoading) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="loading-spinner w-8 h-8"></div>
        </div>
      );
    }
    
    if (!user) {
      return <div>Please log in to access this page.</div>;
    }
    
    if (requiredRole && !requiredRole.includes(user.role)) {
      return <div>You don't have permission to access this page.</div>;
    }
    
    return <Component {...props} />;
  };
};
