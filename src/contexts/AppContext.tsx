import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { AppState, AppAction } from '../types';
import { storage } from '../services/storage';

// App context type
interface AppContextType {
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
  // Helper functions
  refreshData: () => void;
  exportData: () => void;
  importData: (data: string) => boolean;
  resetData: () => void;
}

// Initial state
const initialState: AppState = {
  users: [],
  classes: [],
  subjects: [],
  attendance: [],
  grades: [],
  virtualClasses: [],
  activities: [],
  notifications: [],
  academicYear: null,
  theme: 'light'
};

// App reducer
const appReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    case 'SET_USERS':
      return { ...state, users: action.payload };
    
    case 'ADD_USER':
      return { ...state, users: [...state.users, action.payload] };
    
    case 'UPDATE_USER':
      return {
        ...state,
        users: state.users.map(user =>
          user.id === action.payload.id
            ? { ...user, ...action.payload.updates }
            : user
        )
      };
    
    case 'DELETE_USER':
      return {
        ...state,
        users: state.users.filter(user => user.id !== action.payload)
      };
    
    case 'SET_CLASSES':
      return { ...state, classes: action.payload };
    
    case 'ADD_CLASS':
      return { ...state, classes: [...state.classes, action.payload] };
    
    case 'UPDATE_CLASS':
      return {
        ...state,
        classes: state.classes.map(cls =>
          cls.id === action.payload.id
            ? { ...cls, ...action.payload.updates }
            : cls
        )
      };
    
    case 'DELETE_CLASS':
      return {
        ...state,
        classes: state.classes.filter(cls => cls.id !== action.payload)
      };
    
    case 'SET_ATTENDANCE':
      return { ...state, attendance: action.payload };
    
    case 'ADD_ATTENDANCE':
      return { ...state, attendance: [...state.attendance, action.payload] };
    
    case 'SET_GRADES':
      return { ...state, grades: action.payload };
    
    case 'ADD_GRADE':
      return { ...state, grades: [...state.grades, action.payload] };
    
    case 'SET_VIRTUAL_CLASSES':
      return { ...state, virtualClasses: action.payload };
    
    case 'ADD_VIRTUAL_CLASS':
      return { ...state, virtualClasses: [...state.virtualClasses, action.payload] };
    
    case 'SET_ACTIVITIES':
      return { ...state, activities: action.payload };
    
    case 'ADD_ACTIVITY':
      return { ...state, activities: [...state.activities, action.payload] };
    
    case 'SET_NOTIFICATIONS':
      return { ...state, notifications: action.payload };
    
    case 'ADD_NOTIFICATION':
      return { ...state, notifications: [...state.notifications, action.payload] };
    
    case 'MARK_NOTIFICATION_READ':
      return {
        ...state,
        notifications: state.notifications.map(notification =>
          notification.id === action.payload
            ? { ...notification, isRead: true }
            : notification
        )
      };
    
    case 'SET_THEME':
      return { ...state, theme: action.payload };
    
    case 'SET_ACADEMIC_YEAR':
      return { ...state, academicYear: action.payload };
    
    default:
      return state;
  }
};

// Create context
const AppContext = createContext<AppContextType | undefined>(undefined);

// App provider props
interface AppProviderProps {
  children: ReactNode;
}

// App provider component
export const AppProvider: React.FC<AppProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Load data from storage on mount
  useEffect(() => {
    loadDataFromStorage();
  }, []);

  // Save data to storage whenever state changes
  useEffect(() => {
    if (state.users.length > 0) { // Only save if data is loaded
      storage.saveAppState(state);
    }
  }, [state]);

  // Apply theme to document
  useEffect(() => {
    const root = document.documentElement;
    if (state.theme === 'dark') {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }
  }, [state.theme]);

  // Load data from storage
  const loadDataFromStorage = () => {
    try {
      const appState = storage.getAppState();
      
      // Dispatch all data to state
      dispatch({ type: 'SET_USERS', payload: appState.users });
      dispatch({ type: 'SET_CLASSES', payload: appState.classes });
      dispatch({ type: 'SET_ATTENDANCE', payload: appState.attendance });
      dispatch({ type: 'SET_GRADES', payload: appState.grades });
      dispatch({ type: 'SET_VIRTUAL_CLASSES', payload: appState.virtualClasses });
      dispatch({ type: 'SET_ACTIVITIES', payload: appState.activities });
      dispatch({ type: 'SET_NOTIFICATIONS', payload: appState.notifications });
      dispatch({ type: 'SET_THEME', payload: appState.theme });
      
      if (appState.academicYear) {
        dispatch({ type: 'SET_ACADEMIC_YEAR', payload: appState.academicYear });
      }
    } catch (error) {
      console.error('Error loading data from storage:', error);
    }
  };

  // Refresh data from storage
  const refreshData = () => {
    loadDataFromStorage();
  };

  // Export data
  const exportData = () => {
    try {
      storage.downloadBackup();
    } catch (error) {
      console.error('Error exporting data:', error);
    }
  };

  // Import data
  const importData = (jsonData: string): boolean => {
    try {
      const success = storage.importData(jsonData);
      if (success) {
        loadDataFromStorage();
      }
      return success;
    } catch (error) {
      console.error('Error importing data:', error);
      return false;
    }
  };

  // Reset data to defaults
  const resetData = () => {
    try {
      storage.resetToDefaults();
      loadDataFromStorage();
    } catch (error) {
      console.error('Error resetting data:', error);
    }
  };

  // Context value
  const contextValue: AppContextType = {
    state,
    dispatch,
    refreshData,
    exportData,
    importData,
    resetData
  };

  return (
    <AppContext.Provider value={contextValue}>
      {children}
    </AppContext.Provider>
  );
};

// Custom hook to use app context
export const useApp = (): AppContextType => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};

// Custom hooks for specific data
export const useUsers = () => {
  const { state, dispatch } = useApp();
  
  const addUser = (user: any) => {
    dispatch({ type: 'ADD_USER', payload: user });
  };
  
  const updateUser = (id: string, updates: any) => {
    dispatch({ type: 'UPDATE_USER', payload: { id, updates } });
  };
  
  const deleteUser = (id: string) => {
    dispatch({ type: 'DELETE_USER', payload: id });
  };
  
  return {
    users: state.users,
    addUser,
    updateUser,
    deleteUser
  };
};

export const useClasses = () => {
  const { state, dispatch } = useApp();
  
  const addClass = (classData: any) => {
    dispatch({ type: 'ADD_CLASS', payload: classData });
  };
  
  const updateClass = (id: string, updates: any) => {
    dispatch({ type: 'UPDATE_CLASS', payload: { id, updates } });
  };
  
  const deleteClass = (id: string) => {
    dispatch({ type: 'DELETE_CLASS', payload: id });
  };
  
  return {
    classes: state.classes,
    addClass,
    updateClass,
    deleteClass
  };
};

export const useAttendance = () => {
  const { state, dispatch } = useApp();
  
  const addAttendance = (record: any) => {
    dispatch({ type: 'ADD_ATTENDANCE', payload: record });
  };
  
  return {
    attendance: state.attendance,
    addAttendance
  };
};

export const useGrades = () => {
  const { state, dispatch } = useApp();
  
  const addGrade = (record: any) => {
    dispatch({ type: 'ADD_GRADE', payload: record });
  };
  
  return {
    grades: state.grades,
    addGrade
  };
};

export const useTheme = () => {
  const { state, dispatch } = useApp();
  
  const setTheme = (theme: 'light' | 'dark') => {
    dispatch({ type: 'SET_THEME', payload: theme });
  };
  
  const toggleTheme = () => {
    setTheme(state.theme === 'light' ? 'dark' : 'light');
  };
  
  return {
    theme: state.theme,
    setTheme,
    toggleTheme
  };
};

export const useNotifications = () => {
  const { state, dispatch } = useApp();
  
  const addNotification = (notification: any) => {
    dispatch({ type: 'ADD_NOTIFICATION', payload: notification });
  };
  
  const markAsRead = (id: string) => {
    dispatch({ type: 'MARK_NOTIFICATION_READ', payload: id });
  };
  
  const unreadCount = state.notifications.filter(n => !n.isRead).length;
  
  return {
    notifications: state.notifications,
    addNotification,
    markAsRead,
    unreadCount
  };
};
