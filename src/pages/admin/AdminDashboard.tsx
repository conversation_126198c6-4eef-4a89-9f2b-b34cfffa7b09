import React from 'react';
import { 
  Users, 
  GraduationCap, 
  BookOpen, 
  TrendingUp,
  Calendar,
  User<PERSON>heck,
  Award,
  AlertCircle
} from 'lucide-react';
import { useApp } from '../../contexts/AppContext';
import { StatsCard, Card, CardHeader, CardTitle, CardContent } from '../../components/common/Card';
import { Button } from '../../components/common/Button';

export const AdminDashboard: React.FC = () => {
  const { state } = useApp();

  // Calculate statistics
  const totalStudents = state.users.filter(u => u.role === 'student' && u.isActive).length;
  const totalTeachers = state.users.filter(u => u.role === 'teacher' && u.isActive).length;
  const totalClasses = state.classes.length;
  
  // Calculate attendance rate for today
  const today = new Date().toISOString().split('T')[0];
  const todayAttendance = state.attendance.filter(a => a.date === today);
  const presentToday = todayAttendance.filter(a => a.status === 'present').length;
  const attendanceRate = todayAttendance.length > 0 ? (presentToday / todayAttendance.length) * 100 : 0;

  // Recent activities (mock data for demo)
  const recentActivities = [
    {
      id: 1,
      type: 'student_enrolled',
      message: 'New student Emma Wilson enrolled in Grade 10A',
      time: '2 hours ago',
      icon: GraduationCap
    },
    {
      id: 2,
      type: 'teacher_added',
      message: 'Sarah Johnson added as Math teacher',
      time: '4 hours ago',
      icon: Users
    },
    {
      id: 3,
      type: 'class_created',
      message: 'New class Grade 10B created',
      time: '1 day ago',
      icon: BookOpen
    },
    {
      id: 4,
      type: 'attendance_marked',
      message: 'Attendance marked for Grade 10A',
      time: '2 days ago',
      icon: UserCheck
    }
  ];

  // Quick actions
  const quickActions = [
    {
      title: 'Add Student',
      description: 'Enroll a new student',
      icon: GraduationCap,
      action: () => console.log('Add student')
    },
    {
      title: 'Add Teacher',
      description: 'Add a new teacher',
      icon: Users,
      action: () => console.log('Add teacher')
    },
    {
      title: 'Create Class',
      description: 'Set up a new class',
      icon: BookOpen,
      action: () => console.log('Create class')
    },
    {
      title: 'View Reports',
      description: 'Generate system reports',
      icon: TrendingUp,
      action: () => console.log('View reports')
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-secondary-900">Admin Dashboard</h1>
        <p className="text-secondary-600 mt-1">
          Welcome back! Here's what's happening in your school today.
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Total Students"
          value={totalStudents}
          subtitle="Active students"
          icon={<GraduationCap className="w-6 h-6 text-primary-600" />}
          trend={{ value: 12, isPositive: true }}
        />
        <StatsCard
          title="Total Teachers"
          value={totalTeachers}
          subtitle="Active teachers"
          icon={<Users className="w-6 h-6 text-primary-600" />}
          trend={{ value: 5, isPositive: true }}
        />
        <StatsCard
          title="Total Classes"
          value={totalClasses}
          subtitle="Active classes"
          icon={<BookOpen className="w-6 h-6 text-primary-600" />}
        />
        <StatsCard
          title="Attendance Rate"
          value={`${attendanceRate.toFixed(1)}%`}
          subtitle="Today's attendance"
          icon={<UserCheck className="w-6 h-6 text-primary-600" />}
          trend={{ 
            value: attendanceRate > 85 ? 2 : -3, 
            isPositive: attendanceRate > 85 
          }}
        />
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action, index) => (
              <button
                key={index}
                onClick={action.action}
                className="p-4 border border-secondary-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors text-left"
              >
                <div className="flex items-center mb-2">
                  <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
                    <action.icon className="w-4 h-4 text-primary-600" />
                  </div>
                </div>
                <h3 className="font-medium text-secondary-900 mb-1">
                  {action.title}
                </h3>
                <p className="text-sm text-secondary-600">
                  {action.description}
                </p>
              </button>
            ))}
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activities */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activities</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <activity.icon className="w-4 h-4 text-primary-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-secondary-900">
                      {activity.message}
                    </p>
                    <p className="text-xs text-secondary-500 mt-1">
                      {activity.time}
                    </p>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4 pt-4 border-t border-secondary-200">
              <Button variant="outline" size="sm" fullWidth>
                View All Activities
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* System Alerts */}
        <Card>
          <CardHeader>
            <CardTitle>System Alerts</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start gap-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <AlertCircle className="w-5 h-5 text-yellow-600 flex-shrink-0 mt-0.5" />
                <div>
                  <h4 className="text-sm font-medium text-yellow-800">
                    Low Attendance Alert
                  </h4>
                  <p className="text-sm text-yellow-700 mt-1">
                    Grade 10B has attendance below 80% this week
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <Calendar className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
                <div>
                  <h4 className="text-sm font-medium text-blue-800">
                    Upcoming Event
                  </h4>
                  <p className="text-sm text-blue-700 mt-1">
                    Parent-teacher meeting scheduled for next week
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                <Award className="w-5 h-5 text-green-600 flex-shrink-0 mt-0.5" />
                <div>
                  <h4 className="text-sm font-medium text-green-800">
                    Achievement
                  </h4>
                  <p className="text-sm text-green-700 mt-1">
                    School reached 95% overall attendance this month
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
