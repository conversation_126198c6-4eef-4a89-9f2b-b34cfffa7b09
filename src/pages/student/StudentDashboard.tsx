import React from 'react';
import { 
  Calendar, 
  BarChart3, 
  Award, 
  Video,
  BookOpen,
  TrendingUp,
  Clock,
  Star
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useApp } from '../../contexts/AppContext';
import { StatsCard, Card, CardHeader, CardTitle, CardContent } from '../../components/common/Card';
import { Button } from '../../components/common/Button';

export const StudentDashboard: React.FC = () => {
  const { user } = useAuth();
  const { state } = useApp();

  if (!user || user.role !== 'student') return null;

  // Get student-specific data
  const student = state.users.find(u => u.id === user.id) as any;
  const studentClass = state.classes.find(c => c.students.includes(user.id));

  // Calculate attendance percentage
  const studentAttendance = state.attendance.filter(a => a.studentId === user.id);
  const presentDays = studentAttendance.filter(a => a.status === 'present').length;
  const attendancePercentage = studentAttendance.length > 0 
    ? (presentDays / studentAttendance.length) * 100 
    : 0;

  // Calculate average grade
  const studentGrades = state.grades.filter(g => g.studentId === user.id);
  const averageGrade = studentGrades.length > 0
    ? studentGrades.reduce((acc, grade) => acc + (grade.score / grade.maxScore) * 100, 0) / studentGrades.length
    : 0;

  // Get recent activities
  const recentActivities = [
    {
      id: 1,
      type: 'grade',
      title: 'Math Quiz Grade Posted',
      description: 'Scored 85/100 in Algebra Quiz',
      time: '2 hours ago',
      icon: BarChart3
    },
    {
      id: 2,
      type: 'attendance',
      title: 'Attendance Marked',
      description: 'Present in Physics class',
      time: '1 day ago',
      icon: Calendar
    },
    {
      id: 3,
      type: 'achievement',
      title: 'Achievement Unlocked',
      description: 'Perfect Attendance Week',
      time: '3 days ago',
      icon: Award
    }
  ];

  // Upcoming classes
  const upcomingClasses = [
    {
      id: 1,
      subject: 'Mathematics',
      teacher: 'Ms. Johnson',
      time: '10:00 AM',
      type: 'Regular Class',
      room: 'Room 101'
    },
    {
      id: 2,
      subject: 'Physics',
      teacher: 'Mr. Davis',
      time: '2:00 PM',
      type: 'Virtual Class',
      room: 'Online'
    },
    {
      id: 3,
      subject: 'English',
      teacher: 'Ms. Wilson',
      time: '3:30 PM',
      type: 'Regular Class',
      room: 'Room 203'
    }
  ];

  // Recent grades
  const recentGrades = studentGrades
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    .slice(0, 5)
    .map(grade => {
      const subject = state.subjects.find(s => s.id === grade.subjectId);
      return {
        ...grade,
        subjectName: subject?.name || 'Unknown',
        percentage: (grade.score / grade.maxScore) * 100
      };
    });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center md:text-left">
        <h1 className="text-2xl font-bold text-secondary-900">
          Welcome back, {user.firstName}!
        </h1>
        <p className="text-secondary-600 mt-1">
          {studentClass ? `Grade ${studentClass.grade} - Section ${studentClass.section}` : 'Student Portal'}
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatsCard
          title="Attendance"
          value={`${attendancePercentage.toFixed(1)}%`}
          subtitle={`${presentDays}/${studentAttendance.length} days`}
          icon={<Calendar className="w-6 h-6 text-primary-600" />}
          trend={{ 
            value: attendancePercentage > 90 ? 5 : -2, 
            isPositive: attendancePercentage > 90 
          }}
        />
        <StatsCard
          title="Average Grade"
          value={`${averageGrade.toFixed(1)}%`}
          subtitle="Overall performance"
          icon={<BarChart3 className="w-6 h-6 text-primary-600" />}
          trend={{ 
            value: averageGrade > 80 ? 8 : -3, 
            isPositive: averageGrade > 80 
          }}
        />
        <StatsCard
          title="Achievements"
          value="12"
          subtitle="Badges earned"
          icon={<Award className="w-6 h-6 text-primary-600" />}
        />
        <StatsCard
          title="Class Rank"
          value="#3"
          subtitle="Out of 25 students"
          icon={<Star className="w-6 h-6 text-primary-600" />}
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Today's Classes */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="w-5 h-5" />
              Today's Classes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {upcomingClasses.map((cls) => (
                <div
                  key={cls.id}
                  className="flex items-center justify-between p-3 border border-secondary-200 rounded-lg"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-sm font-medium text-secondary-900">
                        {cls.time}
                      </span>
                      <span className="text-xs bg-primary-100 text-primary-700 px-2 py-1 rounded">
                        {cls.type}
                      </span>
                    </div>
                    <p className="text-sm text-secondary-700 font-medium">
                      {cls.subject}
                    </p>
                    <p className="text-xs text-secondary-500">
                      {cls.teacher} • {cls.room}
                    </p>
                  </div>
                  {cls.type === 'Virtual Class' && (
                    <Button size="sm" variant="primary">
                      <Video className="w-4 h-4 mr-1" />
                      Join
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Grades */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5" />
              Recent Grades
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentGrades.map((grade) => (
                <div
                  key={grade.id}
                  className="flex items-center justify-between p-3 border border-secondary-200 rounded-lg"
                >
                  <div className="flex-1">
                    <p className="text-sm font-medium text-secondary-900">
                      {grade.subjectName}
                    </p>
                    <p className="text-xs text-secondary-500 capitalize">
                      {grade.type} • {new Date(grade.date).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-bold text-secondary-900">
                      {grade.score}/{grade.maxScore}
                    </p>
                    <p className={`text-xs ${
                      grade.percentage >= 90 ? 'text-green-600' :
                      grade.percentage >= 80 ? 'text-blue-600' :
                      grade.percentage >= 70 ? 'text-yellow-600' : 'text-red-600'
                    }`}>
                      {grade.percentage.toFixed(1)}%
                    </p>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4 pt-4 border-t border-secondary-200">
              <Button variant="outline" size="sm" fullWidth>
                View All Grades
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activities */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activities</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentActivities.map((activity) => (
              <div key={activity.id} className="flex items-start gap-3">
                <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <activity.icon className="w-4 h-4 text-primary-600" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-secondary-900">
                    {activity.title}
                  </p>
                  <p className="text-sm text-secondary-600">
                    {activity.description}
                  </p>
                  <p className="text-xs text-secondary-500 mt-1">
                    {activity.time}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" className="h-20 flex-col">
              <Calendar className="w-6 h-6 mb-2" />
              View Attendance
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <BarChart3 className="w-6 h-6 mb-2" />
              Check Grades
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <Award className="w-6 h-6 mb-2" />
              My Achievements
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <BookOpen className="w-6 h-6 mb-2" />
              Study Materials
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
