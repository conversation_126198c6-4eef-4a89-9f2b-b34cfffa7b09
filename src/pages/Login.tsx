import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Mail, Lock, Eye, EyeOff, GraduationCap } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { Button } from '../components/common/Button';
import { Input } from '../components/common/Input';
import { Card } from '../components/common/Card';
import { LoginFormData } from '../types';
import toast from 'react-hot-toast';

export const Login: React.FC = () => {
  const { login, isLoading, error } = useAuth();
  const [showPassword, setShowPassword] = useState(false);
  const [selectedRole, setSelectedRole] = useState<string>('');

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue
  } = useForm<LoginFormData>();

  // Demo accounts for easy access
  const demoAccounts = [
    {
      role: 'admin',
      email: '<EMAIL>',
      password: 'admin123',
      name: 'Ad<PERSON>',
      description: 'Full system access'
    },
    {
      role: 'teacher',
      email: '<EMAIL>',
      password: 'teacher123',
      name: 'Teacher <PERSON>',
      description: 'Math & Science Teacher'
    },
    {
      role: 'student',
      email: '<EMAIL>',
      password: 'student123',
      name: 'Student Portal',
      description: 'Grade 10A Student'
    }
  ];

  const onSubmit = async (data: LoginFormData) => {
    try {
      const success = await login(data.email, data.password, data.rememberMe);
      if (success) {
        toast.success('Login successful!');
      } else {
        toast.error('Invalid credentials');
      }
    } catch (err) {
      toast.error('Login failed. Please try again.');
    }
  };

  const handleDemoLogin = (account: typeof demoAccounts[0]) => {
    setValue('email', account.email);
    setValue('password', account.password);
    setSelectedRole(account.role);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-primary-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-600 rounded-full mb-4">
            <GraduationCap className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-secondary-900 mb-2">Hikmaah</h1>
          <p className="text-secondary-600">Student Management System</p>
        </div>

        {/* Demo Accounts */}
        <Card className="mb-6">
          <h3 className="text-lg font-semibold text-secondary-900 mb-4">
            Demo Accounts
          </h3>
          <div className="space-y-2">
            {demoAccounts.map((account) => (
              <button
                key={account.role}
                onClick={() => handleDemoLogin(account)}
                className={`w-full p-3 text-left rounded-lg border transition-colors ${
                  selectedRole === account.role
                    ? 'border-primary-500 bg-primary-50'
                    : 'border-secondary-200 hover:border-secondary-300 hover:bg-secondary-50'
                }`}
              >
                <div className="font-medium text-secondary-900">
                  {account.name}
                </div>
                <div className="text-sm text-secondary-600">
                  {account.description}
                </div>
                <div className="text-xs text-secondary-500 mt-1">
                  {account.email}
                </div>
              </button>
            ))}
          </div>
        </Card>

        {/* Login Form */}
        <Card>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            {/* Email */}
            <Input
              label="Email"
              type="email"
              leftIcon={Mail}
              placeholder="Enter your email"
              error={errors.email?.message}
              {...register('email', {
                required: 'Email is required',
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: 'Invalid email address'
                }
              })}
            />

            {/* Password */}
            <div className="relative">
              <Input
                label="Password"
                type={showPassword ? 'text' : 'password'}
                leftIcon={Lock}
                placeholder="Enter your password"
                error={errors.password?.message}
                {...register('password', {
                  required: 'Password is required',
                  minLength: {
                    value: 6,
                    message: 'Password must be at least 6 characters'
                  }
                })}
              />
              <button
                type="button"
                className="absolute right-3 top-8 text-secondary-400 hover:text-secondary-600"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
              </button>
            </div>

            {/* Remember Me */}
            <div className="flex items-center">
              <input
                id="rememberMe"
                type="checkbox"
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded"
                {...register('rememberMe')}
              />
              <label htmlFor="rememberMe" className="ml-2 text-sm text-secondary-700">
                Remember me
              </label>
            </div>

            {/* Error Message */}
            {error && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-600">{error}</p>
              </div>
            )}

            {/* Submit Button */}
            <Button
              type="submit"
              fullWidth
              isLoading={isLoading}
              disabled={isLoading}
            >
              Sign In
            </Button>
          </form>

          {/* Footer */}
          <div className="mt-6 text-center">
            <p className="text-xs text-secondary-500">
              Use the demo accounts above for testing
            </p>
          </div>
        </Card>

        {/* System Info */}
        <div className="mt-6 text-center text-xs text-secondary-500">
          <p>Hikmaah Student Management System v1.0</p>
          <p>Built with React + TypeScript + Tailwind CSS</p>
        </div>
      </div>
    </div>
  );
};
