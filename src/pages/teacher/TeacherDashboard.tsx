import React from 'react';
import { 
  <PERSON>, 
  <PERSON>, 
  <PERSON>r<PERSON><PERSON><PERSON>, 
  BarChart3,
  Clock,
  BookOpen,
  Video,
  CheckCircle
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useApp } from '../../contexts/AppContext';
import { StatsCard, Card, CardHeader, CardTitle, CardContent } from '../../components/common/Card';
import { Button } from '../../components/common/Button';

export const TeacherDashboard: React.FC = () => {
  const { user } = useAuth();
  const { state } = useApp();

  if (!user || user.role !== 'teacher') return null;

  // Get teacher-specific data
  const teacher = state.users.find(u => u.id === user.id) as any;
  const teacherClasses = state.classes.filter(c => c.teacherId === user.id);
  const totalStudents = teacherClasses.reduce((acc, cls) => acc + cls.students.length, 0);

  // Calculate today's attendance
  const today = new Date().toISOString().split('T')[0];
  const todayAttendance = state.attendance.filter(a => 
    a.date === today && 
    teacherClasses.some(cls => cls.id === a.classId)
  );
  const presentToday = todayAttendance.filter(a => a.status === 'present').length;
  const attendanceRate = todayAttendance.length > 0 ? (presentToday / todayAttendance.length) * 100 : 0;

  // Get pending tasks
  const pendingTasks = [
    {
      id: 1,
      title: 'Mark Attendance for Grade 10A',
      description: 'Today\'s attendance not yet marked',
      priority: 'high',
      dueTime: '9:00 AM'
    },
    {
      id: 2,
      title: 'Grade Math Quiz',
      description: '15 quizzes pending review',
      priority: 'medium',
      dueTime: 'End of day'
    },
    {
      id: 3,
      title: 'Prepare Virtual Class',
      description: 'Physics class at 2:00 PM',
      priority: 'medium',
      dueTime: '2:00 PM'
    }
  ];

  // Today's schedule
  const todaySchedule = [
    {
      id: 1,
      time: '9:00 AM',
      subject: 'Mathematics',
      class: 'Grade 10A',
      type: 'Regular Class',
      status: 'upcoming'
    },
    {
      id: 2,
      time: '11:00 AM',
      subject: 'Physics',
      class: 'Grade 10B',
      type: 'Lab Session',
      status: 'upcoming'
    },
    {
      id: 3,
      time: '2:00 PM',
      subject: 'Mathematics',
      class: 'Grade 10A',
      type: 'Virtual Class',
      status: 'upcoming'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-secondary-900">
          Welcome back, {user.firstName}!
        </h1>
        <p className="text-secondary-600 mt-1">
          Here's your teaching overview for today.
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="My Classes"
          value={teacherClasses.length}
          subtitle="Active classes"
          icon={<BookOpen className="w-6 h-6 text-primary-600" />}
        />
        <StatsCard
          title="Total Students"
          value={totalStudents}
          subtitle="Across all classes"
          icon={<Users className="w-6 h-6 text-primary-600" />}
        />
        <StatsCard
          title="Today's Attendance"
          value={`${attendanceRate.toFixed(1)}%`}
          subtitle={`${presentToday}/${todayAttendance.length} present`}
          icon={<UserCheck className="w-6 h-6 text-primary-600" />}
          trend={{ 
            value: attendanceRate > 85 ? 5 : -2, 
            isPositive: attendanceRate > 85 
          }}
        />
        <StatsCard
          title="Pending Tasks"
          value={pendingTasks.length}
          subtitle="Items to complete"
          icon={<Clock className="w-6 h-6 text-primary-600" />}
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Today's Schedule */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="w-5 h-5" />
              Today's Schedule
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {todaySchedule.map((item) => (
                <div
                  key={item.id}
                  className="flex items-center justify-between p-3 border border-secondary-200 rounded-lg"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-sm font-medium text-secondary-900">
                        {item.time}
                      </span>
                      <span className="text-xs bg-primary-100 text-primary-700 px-2 py-1 rounded">
                        {item.type}
                      </span>
                    </div>
                    <p className="text-sm text-secondary-700">
                      {item.subject} - {item.class}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    {item.type === 'Virtual Class' && (
                      <Button size="sm" variant="outline">
                        <Video className="w-4 h-4 mr-1" />
                        Join
                      </Button>
                    )}
                    <Button size="sm" variant="ghost">
                      <CheckCircle className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4 pt-4 border-t border-secondary-200">
              <Button variant="outline" size="sm" fullWidth>
                View Full Schedule
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Pending Tasks */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="w-5 h-5" />
              Pending Tasks
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {pendingTasks.map((task) => (
                <div
                  key={task.id}
                  className="flex items-start gap-3 p-3 border border-secondary-200 rounded-lg"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="text-sm font-medium text-secondary-900">
                        {task.title}
                      </h4>
                      <span
                        className={`text-xs px-2 py-1 rounded ${
                          task.priority === 'high'
                            ? 'bg-red-100 text-red-700'
                            : 'bg-yellow-100 text-yellow-700'
                        }`}
                      >
                        {task.priority}
                      </span>
                    </div>
                    <p className="text-sm text-secondary-600 mb-2">
                      {task.description}
                    </p>
                    <p className="text-xs text-secondary-500">
                      Due: {task.dueTime}
                    </p>
                  </div>
                  <Button size="sm" variant="outline">
                    Complete
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Button variant="outline" className="h-20 flex-col">
              <UserCheck className="w-6 h-6 mb-2" />
              Mark Attendance
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <BarChart3 className="w-6 h-6 mb-2" />
              Enter Grades
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <Video className="w-6 h-6 mb-2" />
              Start Virtual Class
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <BookOpen className="w-6 h-6 mb-2" />
              View Classes
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
