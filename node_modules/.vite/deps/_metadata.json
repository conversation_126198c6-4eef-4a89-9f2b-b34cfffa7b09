{"hash": "168d3d18", "configHash": "d14b2726", "lockfileHash": "2d8e87cc", "browserHash": "a707c2ec", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "32b9d518", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "e1819a1e", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "357a325d", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "c30c3a73", "needsInterop": true}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "0fac0a1b", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "debb135c", "needsInterop": true}, "react-hook-form": {"src": "../../react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "a1ebfc84", "needsInterop": false}, "react-hot-toast": {"src": "../../react-hot-toast/dist/index.mjs", "file": "react-hot-toast.js", "fileHash": "c308d04b", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "8a370050", "needsInterop": false}}, "chunks": {"chunk-S3Z6QACX": {"file": "chunk-S3Z6QACX.js"}, "chunk-IYDKXRZQ": {"file": "chunk-IYDKXRZQ.js"}}}